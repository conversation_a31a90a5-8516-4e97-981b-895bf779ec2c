import { cookies } from 'next/headers';

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
}

interface ApiResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

class ServerRequest {
  private baseURL: string;

  constructor() {
    console.log(process.env.NEXT_PUBLIC_API_URL)
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || process.env.API_URL || '/api';
    console.log(this.baseURL)
  }

  private buildURL(url: string, params?: Record<string, any>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          searchParams.set(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      return queryString ? `${fullUrl}?${queryString}` : fullUrl;
    }
    
    return fullUrl;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers['info-planets-token'] = token;
    }
    
    return headers;
  }

  private async request<T>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const { method = 'GET', headers: customHeaders = {}, body, params } = options;
    
    const authHeaders = await this.getAuthHeaders();
    const headers = { ...authHeaders, ...customHeaders };
    
    const requestUrl = this.buildURL(url, params);
    console.log(requestUrl)
    
    const config: RequestInit = {
      method,
      headers,
      cache: 'no-store', // 确保服务端获取最新数据
    };

    if (body && method !== 'GET') {
      config.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(requestUrl, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.code !== 1) {
        throw new Error(data.msg || '请求失败');
      }
      
      return data;
    } catch (error) {
      console.error('Server request error:', error);
      throw error;
    }
  }

  async get<T>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'GET', params });
  }

  async post<T>(url: string, data?: any, params?: Record<string, any>): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'POST', body: data, params });
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'PUT', body: data });
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'DELETE' });
  }
}

export default new ServerRequest(); 