import serverRequest from '../utils/server-request';
import type { Problem } from './base-problem-service';

interface ProblemListResponse {
  items: Problem[];
  total: number;
}

class ServerBaseProblemService {
  async getProblemList(data: {
    pageNum: number;
    pageSize: number;
    difficulty?: number | null;
    type?: number | null;
    category?: number | null;
    search?: string;
    tags?: number[];
    status?: number | null;
    tagIds?: number[];
    categoryId?: number | null;
    searchText?: string;
  }) {
    return serverRequest.post<ProblemListResponse>('/subject/list', data);
  }

  async getProblemDetail(id: string, params: any) {
    return serverRequest.get<Problem>(`/subject/${id}`, params);
  }
}

export default new ServerBaseProblemService();