import { Suspense } from 'react';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import ServerBaseProblemService from '../service/server-base-problem-service';
import ServerTagService from '../service/server-tag-service';
import ProblemListClient from '../components/ProblemListClient';
import type { Problem } from '../service/base-problem-service';
import type { TagGroup } from '../service/tag-service';
export const dynamic = 'force-static';
// 元数据配置
export const metadata: Metadata = {
  title: '基础题库 - 探索编程基础概念和问题解决技巧',
  description: '浏览和练习各种基础编程题目，包括单选题、多选题和判断题。涵盖算法、数据结构、编程语言等多个领域，适合初学者和进阶学习者。',
  keywords: ['编程题库', '算法练习', '基础编程', '编程学习', '代码练习', '算法题', 'JavaScript', 'Python', 'Java'],
  openGraph: {
    title: '基础题库 - 编程学习平台',
    description: '专业的编程题库平台，提供丰富的基础编程题目和详细解析',
    type: 'website',
    url: '/basic-problems',
    images: [
      {
        url: '/og-image-basic-problems.png',
        width: 1200,
        height: 630,
        alt: '基础题库',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '基础题库 - 编程学习平台',
    description: '专业的编程题库平台，提供丰富的基础编程题目和详细解析',
    images: ['/og-image-basic-problems.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: '/basic-problems',
  },
};

// 服务端数据获取函数
async function getInitialData(): Promise<{
  problems: Problem[];
  totalItems: number;
  tagOptions: TagGroup[];
}> {
  try {
    // 并行获取数据
    const [problemsResult, tagsResult] = await Promise.all([
      ServerBaseProblemService.getProblemList({
        pageNum: 1,
        pageSize: 10,
        difficulty: null,
        type: null,
        category: null,
        search: '',
        tags: [],
        status: null,
      }),
      ServerTagService.getTagList(1),
    ]);

    return {
      problems: problemsResult.data.items,
      totalItems: problemsResult.data.total,
      tagOptions: tagsResult.data || [],
    };
  } catch (error) {
    console.error('获取初始数据失败:', error);
    return {
      problems: [],
      totalItems: 0,
      tagOptions: [],
    };
  }
}

// 生成JSON-LD结构化数据
function generateJsonLd(problems: Problem[], totalItems: number) {
  const currentDate = new Date().toISOString();

  return {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: '基础题库',
    description: '专业的编程题库平台，提供丰富的基础编程题目和详细解析',
    url: process.env.NEXT_PUBLIC_SITE_URL + '/basic-problems',
    mainEntity: {
      '@type': 'EducationalOrganization',
      name: '编程学习平台',
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: '基础编程题库',
        numberOfItems: totalItems,
        itemListElement: problems.slice(0, 10).map((problem, index) => ({
          '@type': 'Course',
          position: index + 1,
          name: problem.question,
          description: problem.description || '编程基础练习题',
          educationalLevel: getDifficultyText(problem.difficulty),
          learningResourceType: getTypeText(problem.type),
          keywords: problem.tagViews?.map(tag => tag.name).join(', ') || '',
          dateModified: currentDate,
          url: process.env.NEXT_PUBLIC_SITE_URL + `/basic-problems/${problem.id}`,
        })),
      },
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: '首页',
          item: process.env.NEXT_PUBLIC_SITE_URL,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: '基础题库',
          item: process.env.NEXT_PUBLIC_SITE_URL + '/basic-problems',
        },
      ],
    },
  };
}

// 辅助函数
function getDifficultyText(difficulty: number): string {
  switch (difficulty) {
    case 1: return '简单';
    case 2: return '中等';
    case 3: return '困难';
    default: return '未知';
  }
}

function getTypeText(type: number): string {
  switch (type) {
    case 1: return '单选题';
    case 2: return '多选题';
    case 3: return '判断题';
    default: return '未知类型';
  }
}

// 加载组件
function LoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto px-4 pb-12">
        <div className="py-6">
          <div className="h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {[1, 2, 3].map(i => (
                <div key={i}>
                  <div className="h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
              <div className="md:col-span-2 h-10 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-8">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="border-b border-gray-100 p-4 last:border-b-0">
                <div className="flex gap-2 mb-2">
                  <div className="h-6 bg-gray-200 rounded w-12 animate-pulse"></div>
                  <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="flex gap-4">
                  <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}

// 主要的服务端组件
export default async function BasicProblems() {
  const initialData = await getInitialData();
  const jsonLd = generateJsonLd(initialData.problems, initialData.totalItems);

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(jsonLd),
        }}
      />

      <div className="min-h-screen bg-gray-50">
        <main className="container mx-auto px-4 pb-12">
          {/* 页面标题 */}
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">基础题库</h1>
            <p className="text-gray-600 mt-2">探索各种基础编程概念和问题解决技巧</p>
          </div>

          {/* 客户端交互组件 */}
          <Suspense fallback={<LoadingSkeleton />}>
            <ProblemListClient
              initialProblems={initialData.problems}
              initialTotalItems={initialData.totalItems}
              initialTagOptions={initialData.tagOptions}
            />
          </Suspense>
        </main>
      </div>
    </>
  );
}
