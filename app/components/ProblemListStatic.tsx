import Link from 'next/link';
import { cookies } from 'next/headers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight, faCheckCircle, faUser, faTag } from '@fortawesome/free-solid-svg-icons';
import { SUBJECT_DIFFICULTY_MAP } from '../utils/constant';
import type { Problem } from '../service/base-problem-service';
import type { FilterState } from './ProblemFilterForm';

interface ProblemListStaticProps {
  filters: FilterState;
  problems: Problem[];
  totalItems: number;
  error?: string;
}

export default async function ProblemListStatic({ 
  filters, 
  problems, 
  totalItems, 
  error 
}: ProblemListStaticProps) {
  // 检查用户认证状态
  const cookieStore = await cookies();
  const token = cookieStore.get('token')?.value;
  const isAuthenticated = !!token;

  const getDifficultyColor = (difficulty: number) => {
    return SUBJECT_DIFFICULTY_MAP[difficulty].bgColor + ' ' + SUBJECT_DIFFICULTY_MAP[difficulty].color;
  };

  // 获取题目类型文本
  const getProblemTypeText = (type: number) => {
    switch (type) {
      case 1:
        return '单选题';
      case 2:
        return '多选题';
      case 3:
        return '判断题';
      default:
        return '未知类型';
    }
  };

  // 获取题目类型颜色
  const getProblemTypeColor = (type: number) => {
    switch (type) {
      case 1:
        return 'bg-blue-100 text-blue-800';
      case 2:
        return 'bg-purple-100 text-purple-800';
      case 3:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取完成状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case -1:
        return '未开始';
      case 0:
        return '尝试中';
      case 1:
        return '已解答';
      default:
        return '未知状态';
    }
  };

  // 获取完成状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case -1:
        return 'bg-gray-100 text-gray-800';
      case 0:
        return 'bg-yellow-100 text-yellow-800';
      case 1:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 错误状态
  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 题目列表 */}
      {problems.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="divide-y divide-gray-100">
            {problems.map(problem => (
              <Link key={problem.id} href={`/basic-problems/${problem.id}`} className="block hover:bg-gray-50">
                <div className="p-4 flex items-start justify-between">
                  <div className="w-full pr-4">
                    <div className="flex flex-wrap items-center gap-2 mb-1">
                      <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getDifficultyColor(problem.difficulty)}`}>
                        {SUBJECT_DIFFICULTY_MAP[problem.difficulty].text}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getProblemTypeColor(problem.type)}`}>
                        {getProblemTypeText(problem.type)}
                      </span>
                      {isAuthenticated && (
                        <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getStatusColor(problem.status)}`}>
                          {getStatusText(problem.status)}
                        </span>
                      )}
                    </div>
                    <h4 className="text-lg font-medium text-gray-800 truncate">{problem.question}</h4>
                    <div className="mt-2 flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-gray-500">
                      <span className="flex items-center">
                        <FontAwesomeIcon icon={faCheckCircle} className="mr-1 text-green-500" />
                        正确率: {(problem.interact?.commitAmt && problem.interact?.commitAmt) ? `${(problem.interact.acceptAmt / problem.interact.commitAmt * 100).toFixed(0)}%` : '-'}
                      </span>
                      <span className="flex items-center">
                        <FontAwesomeIcon icon={faUser} className="mr-1" />
                        已完成: {problem.interact?.acceptUserAmt || 0}人
                      </span>
                      {problem.tagViews?.length > 0 && (
                        problem.tagViews.map(tag => (
                          <span key={tag.id} className="flex items-center">
                            <FontAwesomeIcon icon={faTag} className="mr-1" />
                            {tag.name}
                          </span>
                        ))
                      )}
                    </div>
                  </div>
                  <div className="text-indigo-600">
                    <FontAwesomeIcon icon={faChevronRight} />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <p className="text-gray-600">没有找到符合条件的题目</p>
        </div>
      )}
    </div>
  );
}
