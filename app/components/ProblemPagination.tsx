"use client";

import { useRouter, useSearchParams } from 'next/navigation';
import Pagination from './Pagination';

interface ProblemPaginationProps {
  currentPage: number;
  totalItems: number;
  pageSize: number;
}

export default function ProblemPagination({ 
  currentPage, 
  totalItems, 
  pageSize 
}: ProblemPaginationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }
    
    const queryString = params.toString();
    const newURL = queryString ? `?${queryString}` : '/basic-problems';
    router.push(newURL);
  };

  return (
    <Pagination
      currentPage={currentPage}
      totalItems={totalItems}
      pageSize={pageSize}
      onPageChange={handlePageChange}
    />
  );
}
