"use client";

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faChevronRight, faCheckCircle, faUser, faTag, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import BaseProblemService from '../service/base-problem-service';
import { SUBJECT_DIFFICULTY_MAP } from '../utils/constant';
import Pagination from './Pagination';
import TagSelector from './TagSelector';
import CommonDropdown, { DropdownOption } from './CommonDropdown';
import type { Problem } from '../service/base-problem-service';
import type { TagGroup, Tag } from '../service/tag-service';
import { useAppSelector } from '@/app/redux/hooks';
import { selectIsAuthenticated, selectUser } from '@/app/redux/features/authSlice';

interface ProblemListClientProps {
  initialProblems: Problem[];
  initialTotalItems: number;
  initialTagOptions: TagGroup[];
}

export default function ProblemListClient({
  initialProblems,
  initialTotalItems,
  initialTagOptions,
}: ProblemListClientProps) {
  const [problems, setProblems] = useState<Problem[]>(initialProblems);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(initialTotalItems);
  const [filters, setFilters] = useState({
    type: null as null | number,
    difficulty: null as null | number,
    category: null as null | number,
    search: '',
    tags: [] as number[],
    status: null as null | number
  });
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const [isTagSelectorOpen, setIsTagSelectorOpen] = useState(false);
  const tagSelectorRef = useRef<HTMLDivElement | null>(null);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);

  const pageSize = 10;

  // 难度级别选项
  const difficultyOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '简单', value: 1 },
    { label: '中等', value: 2 },
    { label: '困难', value: 3 }
  ];

  // 题目类型选项
  const typeOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '单选题', value: 1 },
    { label: '多选题', value: 2 },
    { label: '判断题', value: 3 }
  ];

  // 状态选项
  const statusOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '未开始', value: 1 },
    { label: '已解答', value: 2 },
    { label: '尝试中', value: 3 }
  ];

  useEffect(() => {
    // 只在筛选条件变化或非首页时才重新获取数据
    if (currentPage !== 1 || 
        filters.type !== null || 
        filters.difficulty !== null || 
        filters.category !== null || 
        filters.search !== '' || 
        filters.tags.length > 0 || 
        filters.status !== null) {
      fetchProblems();
    }
  }, [currentPage, filters.type, filters.difficulty, filters.category, filters.tags, filters.status]);

  // 处理点击外部区域关闭标签选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagSelectorRef.current && !tagSelectorRef.current?.contains(event.target as Node)) {
        setIsTagSelectorOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchProblems = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await BaseProblemService.getProblemList({
        pageNum: currentPage,
        pageSize: 10,
        ...filters,
        tagIds: filters.tags.length > 0 ? filters.tags : undefined,
        categoryId: filters.category,
        difficulty: filters.difficulty,
        searchText: filters.search,
        type: filters.type,
        status: filters.status
      });
      
      setProblems(response.data.items);
      setTotalItems(response.data.total);
    } catch (err) {
      console.error(err);
      setError('获取题目列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleTypeChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      type: value
    }));
    setCurrentPage(1);
  };

  const handleDifficultyChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      difficulty: value
    }));
    setCurrentPage(1);
  };

  const handleCategoryChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      category: value
    }));
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value
    }));
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchProblems();
  };

  const handleTagsChange = (selectedTags: Tag[]) => {
    setFilters(prev => ({
      ...prev,
      tags: selectedTags.map(tag => tag.id)
    }));
    setCurrentPage(1);
    setSelectedTags(selectedTags);
  };

  const handleStatusChange = (value: number | null) => {
    setFilters(prev => ({
      ...prev,
      status: value
    }));
    setCurrentPage(1);
  };

  const toggleTagSelector = () => {
    setIsTagSelectorOpen(!isTagSelectorOpen);
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getDifficultyColor = (difficulty: number) => {
    return SUBJECT_DIFFICULTY_MAP[difficulty].bgColor + ' ' + SUBJECT_DIFFICULTY_MAP[difficulty].color;
  };

  // 获取题目类型文本
  const getProblemTypeText = (type: number) => {
    switch (type) {
      case 1:
        return '单选题';
      case 2:
        return '多选题';
      case 3:
        return '判断题';
      default:
        return '未知类型';
    }
  };

  // 获取题目类型颜色
  const getProblemTypeColor = (type: number) => {
    switch (type) {
      case 1:
        return 'bg-blue-100 text-blue-800';
      case 2:
        return 'bg-purple-100 text-purple-800';
      case 3:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取完成状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case -1:
        return '未开始';
      case 0:
        return '尝试中';
      case 1:
        return '已解答';
      default:
        return '未知状态';
    }
  };

  // 获取完成状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case -1:
        return 'bg-gray-100 text-gray-800';
      case 0:
        return 'bg-yellow-100 text-yellow-800';
      case 1:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 筛选和搜索区域 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        {/* 筛选条件行 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* 题目类型选择器 */}
          <div>
            <label className="block text-gray-700 mb-2">题目类型</label>
            <CommonDropdown<number | null>
              label="题目类型"
              options={typeOptions}
              value={filters.type}
              onChange={handleTypeChange}
              placeholder="全部类型"
              className="w-full"
            />
          </div>

          {/* 难度选择器 */}
          <div>
            <label className="block text-gray-700 mb-2">难度</label>
            <CommonDropdown<number | null>
              label="难度"
              options={difficultyOptions}
              value={filters.difficulty}
              onChange={handleDifficultyChange}
              placeholder="全部难度"
              className="w-full"
            />
          </div>

          {/* 状态选择器 */}
          <div>
            <label className="block text-gray-700 mb-2">状态</label>
            <CommonDropdown<number | null>
              label="状态"
              options={statusOptions}
              value={filters.status}
              onChange={handleStatusChange}
              placeholder="全部状态"
              className="w-full"
              disabled={!isAuthenticated}
            />
            {!isAuthenticated && (
              <p className="text-xs text-gray-500 mt-1">请登录后使用状态筛选</p>
            )}
          </div>
        </div>

        {/* 标签和搜索行 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 标签选择器按钮 */}
          <div className="md:col-span-1">
            <label className="block text-gray-700 mb-2">标签</label>
            <div className="relative" ref={tagSelectorRef}>
              <button
                type="button"
                onClick={toggleTagSelector}
                className="flex items-center justify-between w-full px-4 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-200 transition-colors"
              >
                <div className="flex items-center">
                  <span className="text-gray-700">标签筛选</span>
                  {filters.tags.length > 0 && (
                    <span className="ml-2 flex items-center justify-center h-6 w-6 bg-gray-500 text-white text-xs font-medium rounded-full">
                      {filters.tags.length}
                    </span>
                  )}
                </div>
                <FontAwesomeIcon icon={faChevronDown} className={`text-gray-500 ml-2 transition-transform ${isTagSelectorOpen ? 'transform rotate-180' : ''}`} />
              </button>

              {isTagSelectorOpen && (
                <div className="absolute z-50 left-0 mt-2 w-full md:w-[450px] bg-white rounded-lg shadow-lg border border-gray-200">
                  <TagSelector
                    onTagsChange={handleTagsChange}
                    className="w-full max-h-[400px] overflow-auto"
                    initialSelectedTags={selectedTags || []}
                    tags={initialTagOptions}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 搜索框 */}
          <div className="md:col-span-2">
            <label className="block text-gray-700 mb-2">搜索</label>
            <form onSubmit={handleSearchSubmit} className="relative">
              <input
                type="text"
                placeholder="搜索题目..."
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={filters.search}
                onChange={handleSearchChange}
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-600"
              >
                <FontAwesomeIcon icon={faSearch} />
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
          {error}
        </div>
      )}

      {/* 加载中 */}
      {loading && (
        <div className="bg-white rounded-lg shadow-md p-8 text-center mb-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600 mb-2"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      )}

      {/* 题目列表 */}
      {!loading && !error && (
        <div className="space-y-6">
          {problems.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="divide-y divide-gray-100">
                {problems.map(problem => (
                  <Link key={problem.id} href={`/basic-problems/${problem.id}`} className="block hover:bg-gray-50">
                    <div className="p-4 flex items-start justify-between">
                      <div className="w-full pr-4">
                        <div className="flex flex-wrap items-center gap-2 mb-1">
                          <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getDifficultyColor(problem.difficulty)}`}>
                            {SUBJECT_DIFFICULTY_MAP[problem.difficulty].text}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getProblemTypeColor(problem.type)}`}>
                            {getProblemTypeText(problem.type)}
                          </span>
                          {isAuthenticated && (
                            <span className={`inline-flex px-2 py-1 text-xs rounded-md whitespace-nowrap ${getStatusColor(problem.status)}`}>
                              {getStatusText(problem.status)}
                            </span>
                          )}
                        </div>
                        <h4 className="text-lg font-medium text-gray-800 truncate">{problem.question}</h4>
                        <div className="mt-2 flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-gray-500">
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1 text-green-500" />
                            正确率: {(problem.interact?.commitAmt && problem.interact?.commitAmt) ? `${(problem.interact.acceptAmt / problem.interact.commitAmt * 100).toFixed(0)}%` : '-'}
                          </span>
                          <span className="flex items-center">
                            <FontAwesomeIcon icon={faUser} className="mr-1" />
                            已完成: {problem.interact?.acceptUserAmt || 0}人
                          </span>
                          {problem.tagViews?.length > 0 && (
                            problem.tagViews.map(tag => (
                              <span key={tag.id} className="flex items-center">
                                <FontAwesomeIcon icon={faTag} className="mr-1" />
                                {tag.name}
                              </span>
                            ))
                          )}
                        </div>
                      </div>
                      <div className="text-indigo-600">
                        <FontAwesomeIcon icon={faChevronRight} />
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <p className="text-gray-600">没有找到符合条件的题目</p>
            </div>
          )}
        </div>
      )}

      {/* 分页控制 */}
      {!loading && totalItems > 0 && (
        <Pagination
          currentPage={currentPage}
          totalItems={totalItems}
          pageSize={pageSize}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
} 