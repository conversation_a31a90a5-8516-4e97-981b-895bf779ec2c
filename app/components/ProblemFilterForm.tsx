"use client";

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import TagSelector from './TagSelector';
import CommonDropdown, { DropdownOption } from './CommonDropdown';
import type { TagGroup, Tag } from '../service/tag-service';
import { useAppSelector } from '@/app/redux/hooks';
import { selectIsAuthenticated } from '@/app/redux/features/authSlice';

interface ProblemFilterFormProps {
  initialTagOptions: TagGroup[];
}

export interface FilterState {
  type: number | null;
  difficulty: number | null;
  category: number | null;
  search: string;
  tags: number[];
  status: number | null;
  page: number;
}

export default function ProblemFilterForm({
  initialTagOptions,
}: ProblemFilterFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  
  const [isTagSelectorOpen, setIsTagSelectorOpen] = useState(false);
  const tagSelectorRef = useRef<HTMLDivElement | null>(null);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);

  // 从 URL 参数初始化筛选状态
  const [filters, setFilters] = useState<FilterState>({
    type: searchParams.get('type') ? parseInt(searchParams.get('type')!) : null,
    difficulty: searchParams.get('difficulty') ? parseInt(searchParams.get('difficulty')!) : null,
    category: searchParams.get('category') ? parseInt(searchParams.get('category')!) : null,
    search: searchParams.get('search') || '',
    tags: searchParams.get('tags') ? searchParams.get('tags')!.split(',').map(Number) : [],
    status: searchParams.get('status') ? parseInt(searchParams.get('status')!) : null,
    page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
  });

  // 难度级别选项
  const difficultyOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '简单', value: 1 },
    { label: '中等', value: 2 },
    { label: '困难', value: 3 }
  ];

  // 题目类型选项
  const typeOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '单选题', value: 1 },
    { label: '多选题', value: 2 },
    { label: '判断题', value: 3 }
  ];

  // 状态选项
  const statusOptions: DropdownOption<number | null>[] = [
    { label: '全部', value: null },
    { label: '未开始', value: 1 },
    { label: '已解答', value: 2 },
    { label: '尝试中', value: 3 }
  ];

  // 处理点击外部区域关闭标签选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagSelectorRef.current && !tagSelectorRef.current?.contains(event.target as Node)) {
        setIsTagSelectorOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 初始化选中的标签
  useEffect(() => {
    if (filters.tags.length > 0 && initialTagOptions.length > 0) {
      const allTags = initialTagOptions.flatMap(group => group.tags);
      const selected = allTags.filter(tag => filters.tags.includes(tag.id));
      setSelectedTags(selected);
    }
  }, [filters.tags, initialTagOptions]);

  // 更新 URL 参数
  const updateURL = (newFilters: FilterState) => {
    const params = new URLSearchParams();
    
    if (newFilters.type !== null) params.set('type', newFilters.type.toString());
    if (newFilters.difficulty !== null) params.set('difficulty', newFilters.difficulty.toString());
    if (newFilters.category !== null) params.set('category', newFilters.category.toString());
    if (newFilters.search) params.set('search', newFilters.search);
    if (newFilters.tags.length > 0) params.set('tags', newFilters.tags.join(','));
    if (newFilters.status !== null) params.set('status', newFilters.status.toString());
    if (newFilters.page > 1) params.set('page', newFilters.page.toString());

    const queryString = params.toString();
    const newURL = queryString ? `?${queryString}` : '/basic-problems';
    router.push(newURL);
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = {
      ...filters,
      [key]: value,
      page: 1, // 重置到第一页
    };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newFilters = { ...filters, page: 1 };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleTagsChange = (selectedTags: Tag[]) => {
    const tagIds = selectedTags.map(tag => tag.id);
    handleFilterChange('tags', tagIds);
    setSelectedTags(selectedTags);
  };

  const toggleTagSelector = () => {
    setIsTagSelectorOpen(!isTagSelectorOpen);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      {/* 筛选条件行 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* 题目类型选择器 */}
        <div>
          <label className="block text-gray-700 mb-2">题目类型</label>
          <CommonDropdown<number | null>
            label="题目类型"
            options={typeOptions}
            value={filters.type}
            onChange={(value) => handleFilterChange('type', value)}
            placeholder="全部类型"
            className="w-full"
          />
        </div>

        {/* 难度选择器 */}
        <div>
          <label className="block text-gray-700 mb-2">难度</label>
          <CommonDropdown<number | null>
            label="难度"
            options={difficultyOptions}
            value={filters.difficulty}
            onChange={(value) => handleFilterChange('difficulty', value)}
            placeholder="全部难度"
            className="w-full"
          />
        </div>

        {/* 状态选择器 */}
        <div>
          <label className="block text-gray-700 mb-2">状态</label>
          <CommonDropdown<number | null>
            label="状态"
            options={statusOptions}
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            placeholder="全部状态"
            className="w-full"
            disabled={!isAuthenticated}
          />
          {!isAuthenticated && (
            <p className="text-xs text-gray-500 mt-1">请登录后使用状态筛选</p>
          )}
        </div>
      </div>

      {/* 标签和搜索行 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 标签选择器按钮 */}
        <div className="md:col-span-1">
          <label className="block text-gray-700 mb-2">标签</label>
          <div className="relative" ref={tagSelectorRef}>
            <button
              type="button"
              onClick={toggleTagSelector}
              className="flex items-center justify-between w-full px-4 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-200 transition-colors"
            >
              <div className="flex items-center">
                <span className="text-gray-700">标签筛选</span>
                {filters.tags.length > 0 && (
                  <span className="ml-2 flex items-center justify-center h-6 w-6 bg-gray-500 text-white text-xs font-medium rounded-full">
                    {filters.tags.length}
                  </span>
                )}
              </div>
              <FontAwesomeIcon icon={faChevronDown} className={`text-gray-500 ml-2 transition-transform ${isTagSelectorOpen ? 'transform rotate-180' : ''}`} />
            </button>

            {isTagSelectorOpen && (
              <div className="absolute z-50 left-0 mt-2 w-full md:w-[450px] bg-white rounded-lg shadow-lg border border-gray-200">
                <TagSelector
                  onTagsChange={handleTagsChange}
                  className="w-full max-h-[400px] overflow-auto"
                  initialSelectedTags={selectedTags || []}
                  tags={initialTagOptions}
                />
              </div>
            )}
          </div>
        </div>

        {/* 搜索框 */}
        <div className="md:col-span-2">
          <label className="block text-gray-700 mb-2">搜索</label>
          <form onSubmit={handleSearchSubmit} className="relative">
            <input
              type="text"
              placeholder="搜索题目..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-600"
            >
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
