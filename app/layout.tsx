import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import Script from 'next/script'
import MockWrapper from './components/MockWrapper'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import ReduxProvider from './redux/provider'
import AuthCheck from './components/AuthCheck'
import { ToastProvider } from './components/Toast'
import { ModalProvider } from './components/Modal'
import { RouterListener } from './components/Router'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '信竞星球 - 少儿编程学习平台',
  description: '启发小天才的编程思维，解锁编程奥赛之路',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        {/* 百度统计 */}
        <Script id="baidu-analytics" strategy="afterInteractive">
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?333c681d05021ea4b565709d0488bdd5";
              var s = document.getElementsByTagName("script")[0];
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>
        
        <ReduxProvider>
          <ToastProvider>
            <ModalProvider>
              <AuthCheck />
              <div className="flex flex-col min-h-screen">
                <Navbar />
                <div id="navbar-placeholder" className="h-0 transition-all duration-300"></div>
                <main className="flex-grow">
                  {children}
                </main>
                <Footer />
              </div>
              <RouterListener />
            </ModalProvider>
          </ToastProvider>
        </ReduxProvider>
      </body>
    </html>
  )
}
